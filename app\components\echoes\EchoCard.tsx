import { useState, useEffect } from 'react'
import { Echo } from '~/lib/supabase'
import { useEcho<PERSON> } from '~/hooks/useEchoes'
import { Clock, Heart } from 'lucide-react'
import { format } from 'date-fns'
import clsx from 'clsx'

interface EchoCardProps {
  echo: Echo
}

export function EchoCard({ echo }: EchoCardProps) {
  const { getTimeRemaining } = useEchoes()
  const [timeRemaining, setTimeRemaining] = useState('')
  const [isLiked, setIsLiked] = useState(false)

  useEffect(() => {
    const updateTime = () => {
      setTimeRemaining(getTimeRemaining(echo.expires_at))
    }

    updateTime()
    const interval = setInterval(updateTime, 60000) // 每分钟更新一次

    return () => clearInterval(interval)
  }, [echo.expires_at, getTimeRemaining])

  const handleLike = () => {
    setIsLiked(!isLiked)
  }

  const getRandomGradient = () => {
    const gradients = [
      'from-purple-400 to-pink-400',
      'from-blue-400 to-purple-400',
      'from-pink-400 to-red-400',
      'from-indigo-400 to-blue-400',
      'from-purple-400 to-indigo-400',
      'from-pink-400 to-purple-400',
    ]
    
    // 使用 echo.id 的哈希值来确保同一个回音总是显示相同的渐变
    const hash = echo.id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    
    return gradients[Math.abs(hash) % gradients.length]
  }

  return (
    <div className="group relative">
      {/* 背景装饰 */}
      <div className={clsx(
        "absolute inset-0 bg-gradient-to-br opacity-10 rounded-2xl transform rotate-1 group-hover:rotate-2 transition-transform",
        getRandomGradient()
      )} />
      
      <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
        {/* 内容 */}
        <div className="mb-4">
          <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
            {echo.content}
          </p>
        </div>

        {/* 底部信息 */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4 text-gray-500">
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{timeRemaining}</span>
            </div>
            <div>
              {format(new Date(echo.created_at), 'MM月dd日 HH:mm')}
            </div>
          </div>

          <button
            onClick={handleLike}
            className={clsx(
              "flex items-center space-x-1 px-3 py-1 rounded-full transition-colors",
              isLiked
                ? "bg-red-100 text-red-600"
                : "text-gray-500 hover:bg-gray-100 hover:text-red-500"
            )}
          >
            <Heart className={clsx("w-4 h-4", isLiked && "fill-current")} />
            <span className="text-xs">共鸣</span>
          </button>
        </div>

        {/* 装饰元素 */}
        <div className="absolute top-4 right-4 opacity-20">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 animate-pulse" />
        </div>
      </div>
    </div>
  )
}
