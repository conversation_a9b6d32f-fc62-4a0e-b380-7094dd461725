import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useChronoEntries } from '~/hooks/useChronoEntries'
import { ChronoEntry } from '~/lib/supabase'
import { Save, X, Upload, Music, Image as ImageIcon } from 'lucide-react'

interface JournalFormProps {
  entry?: ChronoEntry | null
  isEdit?: boolean
}

export function JournalForm({ entry, isEdit = false }: JournalFormProps) {
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [imageUrl, setImageUrl] = useState('')
  const [musicUrl, setMusicUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const { createEntry, updateEntry } = useChronoEntries()
  const navigate = useNavigate()

  useEffect(() => {
    if (entry) {
      setTitle(entry.title)
      setContent(entry.content)
      setImageUrl(entry.image_url || '')
      setMusicUrl(entry.music_url || '')
    }
  }, [entry])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title.trim() || !content.trim()) {
      setError('标题和内容不能为空')
      return
    }

    setLoading(true)
    setError('')

    const entryData = {
      title: title.trim(),
      content: content.trim(),
      image_url: imageUrl.trim() || undefined,
      music_url: musicUrl.trim() || undefined,
    }

    let result
    if (isEdit && entry) {
      result = await updateEntry(entry.id, entryData)
    } else {
      result = await createEntry(entryData)
    }

    if (result.error) {
      setError(result.error)
    } else {
      navigate('/journal')
    }

    setLoading(false)
  }

  const handleCancel = () => {
    navigate('/journal')
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {isEdit ? '编辑记录' : '创建新记录'}
          </h1>
          <p className="text-gray-600">
            {isEdit ? '修改你的珍贵回忆' : '记录一个珍贵的回忆瞬间'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 标题 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              标题 *
            </label>
            <input
              id="title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="为这个回忆起个标题..."
              required
            />
          </div>

          {/* 内容 */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
              内容 *
            </label>
            <textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={12}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="写下你的回忆，记录那些珍贵的瞬间..."
              required
            />
            <div className="text-sm text-gray-500 mt-1">
              {content.length} 字符
            </div>
          </div>

          {/* 图片URL */}
          <div>
            <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-2">
              <div className="flex items-center space-x-2">
                <ImageIcon className="w-4 h-4" />
                <span>图片链接（可选）</span>
              </div>
            </label>
            <input
              id="imageUrl"
              type="url"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://example.com/image.jpg"
            />
            {imageUrl && (
              <div className="mt-3">
                <img
                  src={imageUrl}
                  alt="预览"
                  className="w-full max-h-48 object-cover rounded-lg"
                  onError={() => setError('图片链接无效')}
                />
              </div>
            )}
          </div>

          {/* 音乐URL */}
          <div>
            <label htmlFor="musicUrl" className="block text-sm font-medium text-gray-700 mb-2">
              <div className="flex items-center space-x-2">
                <Music className="w-4 h-4" />
                <span>音乐链接（可选）</span>
              </div>
            </label>
            <input
              id="musicUrl"
              type="url"
              value={musicUrl}
              onChange={(e) => setMusicUrl(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://example.com/music.mp3"
            />
            <div className="text-sm text-gray-500 mt-1">
              支持 MP3、WAV 等音频格式
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              className="flex items-center space-x-2 px-6 py-3 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
              <span>取消</span>
            </button>
            
            <button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Save className="w-5 h-5" />
              <span>{loading ? '保存中...' : (isEdit ? '更新记录' : '创建记录')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
