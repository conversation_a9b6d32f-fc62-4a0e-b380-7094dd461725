import { type RouteConfig, index, route } from "@react-router/dev/routes";

export default [
  index("routes/home.tsx"),
  route("auth", "routes/auth.tsx"),
  route("journal", "routes/journal.tsx"),
  route("journal/new", "routes/journal.new.tsx"),
  route("journal/:id", "routes/journal.$id.tsx"),
  route("journal/:id/edit", "routes/journal.$id.edit.tsx"),
  route("echoes", "routes/echoes.tsx"),
] satisfies RouteConfig;
