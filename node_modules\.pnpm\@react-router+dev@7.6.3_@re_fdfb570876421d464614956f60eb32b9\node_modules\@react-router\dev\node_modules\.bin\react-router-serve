#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules/@react-router/serve/node_modules:/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules/@react-router/node_modules:/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules:/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules/@react-router/serve/node_modules:/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules/@react-router/node_modules:/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules:/proc/cygdrive/d/桌面/AI编程/小游戏/Dream's Exit/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules/@react-router/serve/bin.js" "$@"
else
  exec node  "$basedir/../../../../../../@react-router+serve@7.6.3_r_aacb65d9022ed178f2f9fd05c44c23dd/node_modules/@react-router/serve/bin.js" "$@"
fi
