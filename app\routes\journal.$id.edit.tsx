import { useState, useEffect } from 'react'
import { useParams, Navigate } from 'react-router'
import { Layout } from '~/components/layout/Layout'
import { ProtectedRoute } from '~/components/ProtectedRoute'
import { JournalForm } from '~/components/journal/JournalForm'
import { useChronoEntries } from '~/hooks/useChronoEntries'
import { ChronoEntry } from '~/lib/supabase'

export default function EditJournalPage() {
  const { id } = useParams()
  const { getEntry } = useChronoEntries()
  const [entry, setEntry] = useState<ChronoEntry | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchEntry = async () => {
      if (!id) return

      setLoading(true)
      const { data, error } = await getEntry(id)
      
      if (error) {
        setError(error)
      } else {
        setEntry(data)
      }
      
      setLoading(false)
    }

    fetchEntry()
  }, [id, getEntry])

  if (!id) {
    return <Navigate to="/journal" replace />
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </Layout>
      </ProtectedRoute>
    )
  }

  if (error || !entry) {
    return (
      <ProtectedRoute>
        <Layout>
          <div className="max-w-4xl mx-auto">
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error || '记录不存在'}
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <Layout>
        <JournalForm entry={entry} isEdit={true} />
      </Layout>
    </ProtectedRoute>
  )
}
