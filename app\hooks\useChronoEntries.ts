import { useState, useEffect } from 'react'
import { supabase, ChronoEntry } from '~/lib/supabase'
import { useAuthContext } from '~/contexts/AuthContext'

export function useChronoEntries() {
  const [entries, setEntries] = useState<ChronoEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuthContext()

  const fetchEntries = async () => {
    if (!user) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('chrono_entries')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setEntries(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取记录失败')
    } finally {
      setLoading(false)
    }
  }

  const createEntry = async (entry: Omit<ChronoEntry, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user) return { error: '用户未登录' }

    try {
      const { data, error } = await supabase
        .from('chrono_entries')
        .insert([{ ...entry, user_id: user.id }])
        .select()
        .single()

      if (error) throw error
      
      setEntries(prev => [data, ...prev])
      return { data, error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建记录失败'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  const updateEntry = async (id: string, updates: Partial<ChronoEntry>) => {
    if (!user) return { error: '用户未登录' }

    try {
      const { data, error } = await supabase
        .from('chrono_entries')
        .update(updates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single()

      if (error) throw error
      
      setEntries(prev => prev.map(entry => entry.id === id ? data : entry))
      return { data, error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新记录失败'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  const deleteEntry = async (id: string) => {
    if (!user) return { error: '用户未登录' }

    try {
      const { error } = await supabase
        .from('chrono_entries')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) throw error
      
      setEntries(prev => prev.filter(entry => entry.id !== id))
      return { error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除记录失败'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  const getEntry = async (id: string) => {
    if (!user) return { data: null, error: '用户未登录' }

    try {
      const { data, error } = await supabase
        .from('chrono_entries')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取记录失败'
      return { data: null, error: errorMessage }
    }
  }

  useEffect(() => {
    fetchEntries()
  }, [user])

  return {
    entries,
    loading,
    error,
    createEntry,
    updateEntry,
    deleteEntry,
    getEntry,
    refetch: fetchEntries
  }
}
