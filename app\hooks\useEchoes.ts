import { useState, useEffect } from 'react'
import { supabase, Echo } from '~/lib/supabase'

export function useEchoes() {
  const [echoes, setEchoes] = useState<Echo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchEchoes = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('echoes')
        .select('*')
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })

      if (error) throw error
      setEchoes(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取回音失败')
    } finally {
      setLoading(false)
    }
  }

  const createEcho = async (content: string) => {
    try {
      if (content.length > 500) {
        return { error: '内容不能超过500字符' }
      }

      const { data, error } = await supabase
        .from('echoes')
        .insert([{ content }])
        .select()
        .single()

      if (error) throw error
      
      setEchoes(prev => [data, ...prev])
      return { data, error: null }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发布回音失败'
      setError(errorMessage)
      return { error: errorMessage }
    }
  }

  const getTimeRemaining = (expiresAt: string) => {
    const now = new Date()
    const expiry = new Date(expiresAt)
    const diff = expiry.getTime() - now.getTime()
    
    if (diff <= 0) return '已过期'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟后消失`
    } else {
      return `${minutes}分钟后消失`
    }
  }

  useEffect(() => {
    fetchEchoes()
    
    // 设置定时器，每分钟检查一次过期的回音
    const interval = setInterval(() => {
      setEchoes(prev => prev.filter(echo => new Date(echo.expires_at) > new Date()))
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  return {
    echoes,
    loading,
    error,
    createEcho,
    getTimeRemaining,
    refetch: fetchEchoes
  }
}
