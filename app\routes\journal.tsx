import { useState } from 'react'
import { <PERSON> } from 'react-router'
import { Layout } from '~/components/layout/Layout'
import { ProtectedRoute } from '~/components/ProtectedRoute'
import { useChronoEntries } from '~/hooks/useChronoEntries'
import { Plus, BookOpen, Calendar, Music, Image, Trash2, Edit, Eye } from 'lucide-react'
import { format } from 'date-fns'
import clsx from 'clsx'

export default function JournalPage() {
  const { entries, loading, error, deleteEntry } = useChronoEntries()
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这条记录吗？此操作无法撤销。')) return
    
    setDeletingId(id)
    const { error } = await deleteEntry(id)
    if (error) {
      alert('删除失败：' + error)
    }
    setDeletingId(null)
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="max-w-4xl mx-auto">
          {/* 页面头部 */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">时光书简</h1>
              <p className="text-gray-600">记录珍贵回忆，创建专属的情感档案</p>
            </div>
            <Link
              to="/journal/new"
              className="flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
            >
              <Plus className="w-5 h-5" />
              <span>新建记录</span>
            </Link>
          </div>

          {/* 加载状态 */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          )}

          {/* 错误状态 */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {/* 空状态 */}
          {!loading && !error && entries.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">还没有任何记录</h3>
              <p className="text-gray-600 mb-6">开始记录你的第一个珍贵回忆吧</p>
              <Link
                to="/journal/new"
                className="inline-flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-5 h-5" />
                <span>创建第一条记录</span>
              </Link>
            </div>
          )}

          {/* 记录列表 */}
          {!loading && entries.length > 0 && (
            <div className="space-y-6">
              {entries.map((entry) => (
                <div
                  key={entry.id}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {entry.title}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{format(new Date(entry.created_at), 'yyyy年MM月dd日')}</span>
                        </div>
                        {entry.image_url && (
                          <div className="flex items-center space-x-1">
                            <Image className="w-4 h-4" />
                            <span>包含图片</span>
                          </div>
                        )}
                        {entry.music_url && (
                          <div className="flex items-center space-x-1">
                            <Music className="w-4 h-4" />
                            <span>包含音乐</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Link
                        to={`/journal/${entry.id}`}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="查看详情"
                      >
                        <Eye className="w-5 h-5" />
                      </Link>
                      <Link
                        to={`/journal/${entry.id}/edit`}
                        className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        title="编辑"
                      >
                        <Edit className="w-5 h-5" />
                      </Link>
                      <button
                        onClick={() => handleDelete(entry.id)}
                        disabled={deletingId === entry.id}
                        className={clsx(
                          "p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",
                          deletingId === entry.id && "opacity-50 cursor-not-allowed"
                        )}
                        title="删除"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </div>
                  </div>

                  <p className="text-gray-700 leading-relaxed line-clamp-3">
                    {entry.content}
                  </p>

                  {entry.image_url && (
                    <div className="mt-4">
                      <img
                        src={entry.image_url}
                        alt="记录配图"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
