import { useState, useEffect } from 'react'
import { Link, useParams, Navigate } from 'react-router'
import { Layout } from '~/components/layout/Layout'
import { ProtectedRoute } from '~/components/ProtectedRoute'
import { useChronoEntries } from '~/hooks/useChronoEntries'
import { ChronoEntry } from '~/lib/supabase'
import { ArrowLeft, Calendar, Edit, Music, Pause, Play } from 'lucide-react'
import { format } from 'date-fns'

export default function JournalDetailPage() {
  const { id } = useParams()
  const { getEntry } = useChronoEntries()
  const [entry, setEntry] = useState<ChronoEntry | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null)

  useEffect(() => {
    const fetchEntry = async () => {
      if (!id) return

      setLoading(true)
      const { data, error } = await getEntry(id)
      
      if (error) {
        setError(error)
      } else {
        setEntry(data)
      }
      
      setLoading(false)
    }

    fetchEntry()
  }, [id, getEntry])

  useEffect(() => {
    return () => {
      if (audio) {
        audio.pause()
        audio.src = ''
      }
    }
  }, [audio])

  const handlePlayMusic = () => {
    if (!entry?.music_url) return

    if (audio) {
      if (isPlaying) {
        audio.pause()
        setIsPlaying(false)
      } else {
        audio.play()
        setIsPlaying(true)
      }
    } else {
      const newAudio = new Audio(entry.music_url)
      newAudio.addEventListener('ended', () => setIsPlaying(false))
      newAudio.addEventListener('error', () => {
        setIsPlaying(false)
        alert('音乐播放失败')
      })
      setAudio(newAudio)
      newAudio.play()
      setIsPlaying(true)
    }
  }

  if (!id) {
    return <Navigate to="/journal" replace />
  }

  return (
    <ProtectedRoute>
      <Layout>
        <div className="max-w-4xl mx-auto">
          {/* 返回按钮 */}
          <div className="mb-6">
            <Link
              to="/journal"
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>返回书简列表</span>
            </Link>
          </div>

          {/* 加载状态 */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          )}

          {/* 错误状态 */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {/* 记录详情 */}
          {entry && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden">
              {/* 头部 */}
              <div className="p-8 border-b border-gray-200">
                <div className="flex items-start justify-between mb-4">
                  <h1 className="text-3xl font-bold text-gray-900">{entry.title}</h1>
                  <Link
                    to={`/journal/${entry.id}/edit`}
                    className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                    <span>编辑</span>
                  </Link>
                </div>
                
                <div className="flex items-center space-x-6 text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5" />
                    <span>创建于 {format(new Date(entry.created_at), 'yyyy年MM月dd日 HH:mm')}</span>
                  </div>
                  
                  {entry.updated_at !== entry.created_at && (
                    <div className="text-sm">
                      最后更新：{format(new Date(entry.updated_at), 'yyyy年MM月dd日 HH:mm')}
                    </div>
                  )}
                </div>
              </div>

              {/* 内容 */}
              <div className="p-8">
                {/* 图片 */}
                {entry.image_url && (
                  <div className="mb-8">
                    <img
                      src={entry.image_url}
                      alt="记录配图"
                      className="w-full max-h-96 object-cover rounded-lg shadow-lg"
                    />
                  </div>
                )}

                {/* 音乐播放器 */}
                {entry.music_url && (
                  <div className="mb-8 p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={handlePlayMusic}
                        className="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                      >
                        {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                      </button>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Music className="w-5 h-5 text-gray-600" />
                          <span className="text-gray-700">背景音乐</span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {isPlaying ? '正在播放...' : '点击播放'}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 文字内容 */}
                <div className="prose prose-lg max-w-none">
                  <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                    {entry.content}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  )
}
